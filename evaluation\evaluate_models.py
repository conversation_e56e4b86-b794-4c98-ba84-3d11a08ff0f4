import pandas as pd
import json
import os
import sys
sys.path.append('../training')

from training.train_regex import RegexClassifier
from training.train_tfidf import TfidfClassifier
from training.train_balanced import BalancedClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

def load_test_data():
    """Load test data"""
    test_df = pd.read_csv('../data/processed/test.csv')
    test_bal_df = pd.read_csv('../data/processed/test_balanced.csv')
    return test_df, test_bal_df

def evaluate_regex_model(test_df):
    """Evaluate regex model"""
    print("Evaluating Regex Model...")
    
    # Load model
    classifier = RegexClassifier()
    classifier.load('../models/regex_model.pkl')
    
    # Predict
    predictions = [classifier.predict(text) for text in test_df['Titre']]

    # Calculate metrics
    accuracy = accuracy_score(test_df['Catégorie'], predictions)
    report = classification_report(test_df['Catégorie'], predictions, output_dict=True)
    
    print(f"Regex Accuracy: {accuracy:.4f}")
    
    return {
        'model': 'regex',
        'accuracy': accuracy,
        'classification_report': report
    }

def evaluate_tfidf_model(test_df):
    """Evaluate TF-IDF model"""
    print("Evaluating TF-IDF Model...")
    
    # Load model
    classifier = TfidfClassifier()
    classifier.load('../models/tfidf_model.pkl')
    
    # Predict
    predictions = [classifier.predict(text) for text in test_df['Titre']]

    # Calculate metrics
    accuracy = accuracy_score(test_df['Catégorie'], predictions)
    report = classification_report(test_df['Catégorie'], predictions, output_dict=True)
    
    print(f"TF-IDF Accuracy: {accuracy:.4f}")
    
    return {
        'model': 'tfidf',
        'accuracy': accuracy,
        'classification_report': report
    }

def evaluate_balanced_model(test_bal_df):
    """Evaluate balanced model"""
    print("Evaluating Balanced Model...")
    
    # Load model
    classifier = BalancedClassifier()
    classifier.load('../models/balanced_model.pkl')
    
    # Predict
    predictions = [classifier.predict(text) for text in test_bal_df['Titre']]

    # Calculate metrics
    accuracy = accuracy_score(test_bal_df['Catégorie'], predictions)
    report = classification_report(test_bal_df['Catégorie'], predictions, output_dict=True)
    
    print(f"Balanced Accuracy: {accuracy:.4f}")
    
    return {
        'model': 'balanced',
        'accuracy': accuracy,
        'classification_report': report
    }

def compare_models():
    """Compare all models"""
    print("=" * 60)
    print("MODEL EVALUATION RESULTS")
    print("=" * 60)
    
    # Load test data
    test_df, test_bal_df = load_test_data()
    
    results = []
    
    # Evaluate each model
    try:
        regex_results = evaluate_regex_model(test_df)
        results.append(regex_results)
    except Exception as e:
        print(f"Error evaluating regex model: {e}")
    
    try:
        tfidf_results = evaluate_tfidf_model(test_df)
        results.append(tfidf_results)
    except Exception as e:
        print(f"Error evaluating TF-IDF model: {e}")
    
    try:
        balanced_results = evaluate_balanced_model(test_bal_df)
        results.append(balanced_results)
    except Exception as e:
        print(f"Error evaluating balanced model: {e}")
    
    # Print comparison
    print("\n" + "=" * 60)
    print("ACCURACY COMPARISON")
    print("=" * 60)
    
    for result in results:
        print(f"{result['model'].upper()}: {result['accuracy']:.4f}")
    
    # Save results
    os.makedirs('../evaluation', exist_ok=True)
    with open('../evaluation/evaluation_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nResults saved to ../evaluation/evaluation_results.json")
    
    # Determine best model
    if results:
        best_model = max(results, key=lambda x: x['accuracy'])
        print(f"\nBest performing model: {best_model['model'].upper()} ({best_model['accuracy']:.4f})")
    
    return results

if __name__ == "__main__":
    compare_models()
