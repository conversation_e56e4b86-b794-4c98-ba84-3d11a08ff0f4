from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import logging
import sys
import os

# Add inference path
sys.path.append('inference')
from classifier import UnifiedClassifier
from rag_system import RAGSystem

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Ticket Classification & RAG API",
    description="""
## Ticket Classification & RAG API

### Features:
- **Classification**: Multi-model ticket classification (Regex, TF-IDF, Balanced)
- **RAG System**: LangChain + Ollama LLM for intelligent responses
- **11 Categories**: SAGE, SLV, MAXIMO, APPLICATIONS, Windows, Imprimante, Cartouche, Réseaux, Téléphonie Voip, <PERSON><PERSON><PERSON> d'accès, CAMERA

### Models:
- **regex**: Rule-based patterns (Recommended)
- **tfidf**: TF-IDF + Logistic Regression
- **balanced**: Balanced dataset approach

### RAG Technology:
- **LLM**: Ollama hermes3:3b
- **Vector Store**: FAISS
- **Embeddings**: HuggingFace all-MiniLM-L6-v2
    """,
    version="1.0.0"
)

# Initialize systems
try:
    classifier = UnifiedClassifier()
    classifier_loaded = True
    logger.info("Classifier loaded successfully")
except Exception as e:
    logger.error(f"Error loading classifier: {e}")
    classifier_loaded = False

try:
    rag_system = RAGSystem()
    rag_loaded = rag_system.initialize()
    logger.info(f"RAG system loaded: {rag_loaded}")
except Exception as e:
    logger.error(f"Error loading RAG system: {e}")
    rag_loaded = False

# Request Models
class ClassificationRequest(BaseModel):
    text: str = Field(..., description="Text to classify", example="Correction BL 3514")
    model: str = Field(default="regex", description="Model to use: regex, tfidf, balanced, all")

class QuestionRequest(BaseModel):
    question: str = Field(..., description="Question to ask", example="Comment créer un bon de commande?")
    model: str = Field(default="regex", description="Classification model: regex, tfidf, balanced")

# Response Models
class ClassificationResponse(BaseModel):
    text: str
    prediction: str
    probabilities: dict
    model_used: str

class QuestionResponse(BaseModel):
    question: str
    category: str
    answer: str
    model_used: str

# API Endpoints
@app.post("/classify", response_model=ClassificationResponse, tags=["Classification"])
async def classify_text(request: ClassificationRequest):
    """
    Classify ticket text into categories using selected model
    
    **Available Models:**
    - **regex**: Rule-based classification (Recommended)
    - **tfidf**: TF-IDF + Logistic Regression
    - **balanced**: Balanced dataset approach
    - **all**: Compare all models
    """
    if not classifier_loaded:
        raise HTTPException(status_code=500, detail="Classifier not loaded")
    
    try:
        if request.model == "all":
            results = classifier.predict_all(request.text)
            # Use regex as primary prediction
            primary_result = results.get('regex', list(results.values())[0])
            return ClassificationResponse(
                text=request.text,
                prediction=primary_result['prediction'],
                probabilities=primary_result['probabilities'],
                model_used="all"
            )
        else:
            result = classifier.predict(request.text, request.model)
            return ClassificationResponse(
                text=request.text,
                prediction=result['prediction'],
                probabilities=result['probabilities'],
                model_used=request.model
            )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ask", response_model=QuestionResponse, tags=["RAG"])
async def ask_question(request: QuestionRequest):
    """
    Ask a question and get an intelligent response using RAG + LLM
    
    **How it works:**
    1. Classify the question using selected model
    2. Use LangChain RAG with Ollama LLM to generate response
    3. Return category + intelligent answer
    
    **RAG Technology:**
    - **LLM**: Ollama hermes3:3b
    - **Vector Search**: FAISS with semantic similarity
    - **Knowledge Base**: CSV-based Q&A pairs
    """
    if not classifier_loaded:
        raise HTTPException(status_code=500, detail="Classifier not loaded")
    
    if not rag_loaded:
        raise HTTPException(status_code=500, detail="RAG system not loaded")
    
    try:
        # Classify question
        classification = classifier.predict(request.question, request.model)
        category = classification['prediction']
        
        # Get RAG answer
        answer = rag_system.get_answer(request.question)
        
        return QuestionResponse(
            question=request.question,
            category=category,
            answer=answer,
            model_used=request.model
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models", tags=["Info"])
async def get_models():
    """Get available classification models"""
    if not classifier_loaded:
        return {"error": "Classifier not loaded"}
    
    return {
        "models": classifier.get_available_models(),
        "recommended": "regex"
    }

@app.get("/categories", tags=["Info"])
async def get_categories():
    """Get available categories"""
    if not classifier_loaded:
        return {"error": "Classifier not loaded"}
    
    return {
        "categories": classifier.get_categories(),
        "count": len(classifier.get_categories())
    }

@app.get("/health", tags=["Info"])
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "classifier_loaded": classifier_loaded,
        "rag_loaded": rag_loaded,
        "models": classifier.get_available_models() if classifier_loaded else [],
        "categories": len(classifier.get_categories()) if classifier_loaded else 0
    }

@app.get("/", tags=["Info"])
async def root():
    """API information"""
    return {
        "message": "Ticket Classification & RAG API",
        "version": "1.0.0",
        "endpoints": {
            "classify": "POST /classify - Classify ticket text",
            "ask": "POST /ask - Ask question with RAG",
            "models": "GET /models - Available models",
            "categories": "GET /categories - Available categories",
            "health": "GET /health - Health check",
            "docs": "GET /docs - API documentation"
        },
        "status": {
            "classifier": "loaded" if classifier_loaded else "error",
            "rag": "loaded" if rag_loaded else "error"
        }
    }

if __name__ == "__main__":
    import uvicorn
    logger.info("Starting API server...")
    uvicorn.run(app, host="127.0.0.1", port=8000)
