import os
import sys

# Add the training directory to the path
training_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'training')
sys.path.append(training_path)

from train_regex import RegexClassifier
from train_tfidf import TfidfClassifier
from train_balanced import BalancedClassifier

class UnifiedClassifier:
    def __init__(self):
        self.models = {}
        self.load_models()
    
    def load_models(self):
        """Load all available models"""
        model_configs = [
            ('regex', RegexClassifier, 'models/regex_model.pkl'),
            ('tfidf', TfidfClassifier, 'models/tfidf_model.pkl'),
            ('balanced', BalancedClassifier, 'models/balanced_model.pkl')
        ]
        
        for name, model_class, path in model_configs:
            try:
                if os.path.exists(path):
                    model = model_class()
                    model.load(path)
                    self.models[name] = model
                    print(f"Loaded {name} model")
                else:
                    print(f"Model file not found: {path}")
            except Exception as e:
                print(f"Error loading {name} model: {e}")
        
        print(f"Loaded {len(self.models)} models: {list(self.models.keys())}")
    
    def predict(self, text, model_name='regex'):
        """Predict using specified model"""
        if model_name not in self.models:
            raise ValueError(f"Model '{model_name}' not available. Available models: {list(self.models.keys())}")
        
        model = self.models[model_name]
        prediction = model.predict(text)
        probabilities = model.predict_proba(text)
        
        return {
            'prediction': prediction,
            'probabilities': probabilities,
            'model': model_name
        }
    
    def predict_all(self, text):
        """Predict using all available models"""
        results = {}
        
        for model_name in self.models:
            try:
                result = self.predict(text, model_name)
                results[model_name] = result
            except Exception as e:
                print(f"Error with {model_name} model: {e}")
        
        return results
    
    def get_best_prediction(self, text, preferred_model='regex'):
        """Get best prediction with fallback"""
        # Try preferred model first
        if preferred_model in self.models:
            try:
                return self.predict(text, preferred_model)
            except Exception as e:
                print(f"Error with preferred model {preferred_model}: {e}")
        
        # Try other models
        for model_name in self.models:
            try:
                return self.predict(text, model_name)
            except Exception as e:
                print(f"Error with {model_name} model: {e}")
        
        # Fallback
        return {
            'prediction': 'APPLICATIONS',
            'probabilities': {'APPLICATIONS': 1.0},
            'model': 'fallback'
        }
    
    def get_available_models(self):
        """Get list of available models"""
        return list(self.models.keys())
    
    def get_categories(self, model_name='regex'):
        """Get categories for a specific model"""
        if model_name not in self.models:
            return []
        
        model = self.models[model_name]
        if hasattr(model, 'categories') and model.categories:
            return model.categories
        elif hasattr(model, 'patterns'):
            return list(model.patterns.keys())
        else:
            return []

def main():
    """Test the unified classifier"""
    classifier = UnifiedClassifier()
    
    test_examples = [
        "Correction BL 3514 du 18/02/2025",
        "BCSCG01250455 à signer",
        "Problème de connexion Outlook",
        "Installation imprimante HP",
        "Comment changer cartouche"
    ]
    
    print("\n" + "=" * 60)
    print("TESTING UNIFIED CLASSIFIER")
    print("=" * 60)
    
    for text in test_examples:
        print(f"\nText: {text}")
        print("-" * 40)
        
        # Test all models
        results = classifier.predict_all(text)
        for model_name, result in results.items():
            print(f"{model_name.upper()}: {result['prediction']} (conf: {max(result['probabilities'].values()):.3f})")
        
        # Get best prediction
        best = classifier.get_best_prediction(text)
        print(f"BEST: {best['prediction']} using {best['model']} model")

if __name__ == "__main__":
    main()
