#!/usr/bin/env python3
"""
Script simple pour évaluer les modèles de classification
Usage: python evaluate.py
"""

import subprocess
import sys
import os

def main():
    """Lance l'évaluation des modèles"""
    print("🚀 Lancement de l'évaluation des modèles...")
    print("=" * 60)
    
    try:
        # Exécuter le script d'évaluation
        result = subprocess.run([
            sys.executable, 
            "evaluation/evaluate_models.py"
        ], capture_output=True, text=True)
        
        # Afficher la sortie
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ Warnings:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ Évaluation terminée avec succès!")
            print("📊 Consultez le rapport détaillé: evaluation/model_evaluation_report.md")
            print("📋 Résultats JSON: evaluation/evaluation_results.json")
        else:
            print(f"\n❌ Erreur lors de l'évaluation (code: {result.returncode})")
            return 1
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
